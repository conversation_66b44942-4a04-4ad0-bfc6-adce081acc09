{"version": 3, "file": "puppeteer.js", "sourceRoot": "", "sources": ["../../../src/puppeteer.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAGH,sFAAoE;AACpE,2EAAyD;AACzD,sFAAoE;AACpE,iFAA+D;AAC/D,oFAA8E;AAAtE,mHAAA,cAAc,OAAA;AAItB,uEAAiE;AACjE,oFAA4E;AAC5E,kEAAyE;AACzE,uEAAyE;AAEzE,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAY,CAAC;AAElE,IAAI,iBAAyB,CAAC;AAC9B,QAAQ,WAAW,EAAE;IACnB,KAAK,SAAS;QACZ,iBAAiB,GAAG,kCAAmB,CAAC,OAAO,CAAC;QAChD,MAAM;IACR;QACE,iBAAiB,GAAG,kCAAmB,CAAC,QAAQ,CAAC;CACpD;AAED;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,gCAAa,CAAC;IAClC,WAAW,EAAE,IAAA,6BAAmB,EAAC,0BAAW,CAAC;IAC7C,iBAAiB;IACjB,eAAe,EAAE,KAAK;IACtB,WAAW;CACZ,CAAC,CAAC;AAGD,eAAO,GAKL,SAAS,UAJX,4BAAoB,GAIlB,SAAS,uBAHX,mBAAW,GAGT,SAAS,cAFX,sBAAc,GAEZ,SAAS,iBADX,cAAM,GACJ,SAAS,QAAC;AAEd,kBAAe,SAAS,CAAC"}