{"version": 3, "file": "puppeteer.js", "sourceRoot": "", "sources": ["../../../src/puppeteer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,cAAc,qDAAqD,CAAC;AACpE,cAAc,0CAA0C,CAAC;AACzD,cAAc,qDAAqD,CAAC;AACpE,cAAc,gDAAgD,CAAC;AAC/D,OAAO,EAAC,cAAc,EAAC,MAAM,gDAAgD,CAAC;AAI9E,OAAO,EAAC,WAAW,EAAC,MAAM,sCAAsC,CAAC;AACjE,OAAO,EAAC,aAAa,EAAC,MAAM,+CAA+C,CAAC;AAC5E,OAAO,EAAC,mBAAmB,EAAC,MAAM,sCAAsC,CAAC;AACzE,OAAO,EAAC,mBAAmB,EAAC,MAAM,sCAAsC,CAAC;AAEzE,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAY,CAAC;AAElE,IAAI,iBAAyB,CAAC;AAC9B,QAAQ,WAAW,EAAE;IACnB,KAAK,SAAS;QACZ,iBAAiB,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAChD,MAAM;IACR;QACE,iBAAiB,GAAG,mBAAmB,CAAC,QAAQ,CAAC;CACpD;AAED;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC;IAClC,WAAW,EAAE,mBAAmB,CAAC,WAAW,CAAC;IAC7C,iBAAiB;IACjB,eAAe,EAAE,KAAK;IACtB,WAAW;CACZ,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,EACX,OAAO,EACP,oBAAoB,EACpB,WAAW,EACX,cAAc,EACd,MAAM,GACP,GAAG,SAAS,CAAC;AAEd,eAAe,SAAS,CAAC"}