{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAIH,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,KAAK,EAAC,IAAI,EAAC,MAAM,WAAW,CAAC;AACpC,OAAO,KAAK,EAAC,MAAM,EAAC,MAAM,qBAAqB,CAAC;AAEhD;;;;GAIG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;CAC5B;AAED;;GAEG;AACH,oBAAY,oBAAoB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE9D;;GAEG;AACH,oBAAY,oBAAoB,GAAG,CACjC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,KAC/B,OAAO,CAAC;AAEb;;GAEG;AACH,oBAAY,oBAAoB,GAAG,CACjC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,KAC/B,OAAO,CAAC;AAEb;;GAEG;AACH,eAAO,MAAM,qCAAqC,kDAwBhD,CAAC;AAEH;;GAEG;AACH,oBAAY,UAAU,GAClB,aAAa,GACb,MAAM,GACN,eAAe,GACf,QAAQ,GACR,YAAY,GACZ,iBAAiB,GACjB,sBAAsB,GACtB,eAAe,GACf,WAAW,GACX,cAAc,GACd,sBAAsB,GACtB,gBAAgB,GAChB,iBAAiB,GACjB,iBAAiB,GACjB,oBAAoB,GACpB,gBAAgB,GAChB,YAAY,CAAC;AAEjB;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;;;GAIG;AACH,0BAAkB,oBAAoB;IACpC;;;;;;;OAOG;IACH,YAAY,iBAAiB;IAE7B;;;;;;OAMG;IACH,aAAa,kBAAkB;IAE/B;;;;;;;;;;OAUG;IACH,aAAa,kBAAkB;IAC/B;;;;;;;OAOG;IACH,eAAe,oBAAoB;CACpC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,qBAAa,OAAQ,SAAQ,YAAY;IACvC;;OAEG;;IAKH;;OAEG;IACH,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIxB;;OAEG;IACH,OAAO,IAAI,IAAI;IAIf;;OAEG;IACH,IAAI,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAElC;IAED;;;OAGG;IACH,OAAO,IAAI,YAAY,GAAG,IAAI;IAI9B;;OAEG;IACH,wBAAwB,IAAI,oBAAoB,GAAG,SAAS;IAI5D;;;;;;;;;;;;;;;;;OAiBG;IACH,6BAA6B,CAC3B,OAAO,CAAC,EAAE,qBAAqB,GAC9B,OAAO,CAAC,cAAc,CAAC;IAK1B;;;OAGG;IACH,eAAe,IAAI,cAAc,EAAE;IAInC;;OAEG;IACH,qBAAqB,IAAI,cAAc;IAIvC;;OAEG;IACH,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKlD;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU,IAAI,MAAM;IAIpB;;;OAGG;IACH,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIxB;;OAEG;IACH,oBAAoB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKvD;;;OAGG;IACH,OAAO,IAAI,MAAM,EAAE;IAInB;;OAEG;IACH,MAAM,IAAI,MAAM;IAIhB;;;;;;;;;;;;;;;;OAgBG;IACH,aAAa,CACX,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,CAAC,EAAE,oBAAoB,GAC7B,OAAO,CAAC,MAAM,CAAC;IAKlB;;;;;;;;OAQG;IACH,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAIxB;;;;;;;;;OASG;IACH,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAI1B;;;OAGG;IACH,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAI5B;;;OAGG;IACH,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAItB;;;;OAIG;IACH,UAAU,IAAI,IAAI;IAIlB;;OAEG;IACH,WAAW,IAAI,OAAO;CAGvB;AACD;;GAEG;AACH,0BAAkB,2BAA2B;IAC3C;;;OAGG;IACH,aAAa,kBAAkB;IAE/B;;;;;;;OAOG;IACH,aAAa,kBAAkB;IAC/B;;;OAGG;IACH,eAAe,oBAAoB;CACpC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC9C;;OAEG;;IAKH;;OAEG;IACH,OAAO,IAAI,MAAM,EAAE;IAInB;;;;;;;;;;;;;;;;;;;OAmBG;IACH,aAAa,CACX,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,CAAC,EAAE;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAC,GAC3B,OAAO,CAAC,MAAM,CAAC;IAKlB;;;;;;OAMG;IACH,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAIxB;;;;;;OAMG;IACH,WAAW,IAAI,OAAO;IAItB;;;;;;;;;;;;;OAaG;IACH,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAK7E;;;;;;;;;;;OAWG;IACH,wBAAwB,IAAI,OAAO,CAAC,IAAI,CAAC;IAIzC;;OAEG;IACH,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIxB;;OAEG;IACH,OAAO,IAAI,OAAO;IAIlB;;;;;;OAMG;IACH,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAGvB"}