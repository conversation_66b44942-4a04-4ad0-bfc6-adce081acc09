<?php

namespace App\Services\SocialMediaServices;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\WhatsApp\WhatsAppMessage;

class WhatsAppCacheService
{
    // Cache TTL constants
    const BUSINESS_CACHE_TTL = 72000; // 20 hours in seconds
    const SESSION_CACHE_TTL = 1800;   // 30 minutes in seconds
    const MAX_CACHED_MESSAGES = 20;   // Maximum messages to keep in cache

    // Cache key prefixes
    const BUSINESS_KEY_PREFIX = 'whatsapp:business:';
    const SESSION_KEY_PREFIX = 'whatsapp:session:';

    /**
     * Get complete business data with all relations from cache or database
     * Cache for 20 hours using user_id as key
     */
    public static function getBusinessData($userId)
    {
        try {
            $cacheKey = self::BUSINESS_KEY_PREFIX . $userId;

            // Try to get from cache first
            $cachedData = Cache::get($cacheKey);

            if ($cachedData) {
                Log::info("✅ Complete business data retrieved from cache", ['user_id' => $userId]);
                return $cachedData;
            }

            // Not in cache, get from database with all relations
            Log::info("📊 Fetching complete business data from database", ['user_id' => $userId]);

            $business = \App\Models\Business::where('user_id', $userId)
                ->with(['doctors.clinic', 'clinics.doctors', 'clinics.offers', 'clinics.services'])
                ->first();

            if (!$business) {
                Log::error('Business not found for user', ['user_id' => $userId]);
                return null;
            }

            // Build complete business data with all relations
            $businessData = [
                'name' => $business->name,
                'description' => $business->description ?? '',
                'phone' => $business->phone ?? '',
                'email' => $business->email ?? '',
                'address' => $business->address ?? '',
                'city' => $business->city ?? '',
                'website' => $business->website ?? '',
                'whatsapp_number' => $business->whatsapp_number ?? '',

                'doctors' => $business->doctors->map(function($doctor) {
                    return [
                        'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                        'specialization' => $doctor->specialization,
                        'clinic' => $doctor->clinic->name ?? 'غير محدد',
                        'available' => $doctor->is_available ? 'متاح' : 'غير متاح',
                        'experience' => $doctor->experience ?? '',
                        'education' => $doctor->education ?? '',
                    ];
                })->toArray(),

                'clinics' => $business->clinics->map(function($clinic) {
                    return [
                        'name' => $clinic->name,
                        'description' => $clinic->description ?? '',
                        'floor' => $clinic->floor_number ?? '',
                        'room' => $clinic->room_number ?? '',
                        'phone' => $clinic->phone_number ?? '',
                        'doctors_count' => $clinic->doctors->count(),
                        'doctors' => $clinic->doctors->pluck('first_name')->toArray(),
                        'services' => $clinic->services->map(function($service) {
                            return [
                                'name' => $service->name,
                                'description' => $service->description ?? '',
                                'price' => $service->price ?? 'غير محدد',
                                'is_active' => $service->is_active ?? true,
                            ];
                        })->toArray(),
                        'offers' => $clinic->offers->map(function($offer) {
                            return [
                                'title' => $offer->title,
                                'description' => $offer->description ?? '',
                                'start_date' => $offer->start_date ? $offer->start_date->format('Y-m-d') : null,
                                'end_date' => $offer->end_date ? $offer->end_date->format('Y-m-d') : null,
                                'offer_url' => $offer->offer_url ?? '',
                            ];
                        })->toArray(),
                    ];
                })->toArray(),

                'cached_at' => now()->toISOString()
            ];

            // Store in cache for 20 hours
            Cache::put($cacheKey, $businessData, self::BUSINESS_CACHE_TTL);

            Log::info("💾 Complete business data cached successfully", [
                'user_id' => $userId,
                'doctors_count' => count($businessData['doctors']),
                'clinics_count' => count($businessData['clinics'])
            ]);

            return $businessData;

        } catch (\Exception $e) {
            Log::error('Error in getBusinessData cache: ' . $e->getMessage(), [
                'user_id' => $userId,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get customer session from cache
     * Returns complete session data including messages, appointment info, etc.
     */
    public static function getCustomerSession($whatsappId)
    {
        try {
            $cacheKey = self::SESSION_KEY_PREFIX . $whatsappId;

            // Try to get from cache first
            $sessionData = Cache::get($cacheKey);

            if ($sessionData) {
                // Extend cache TTL on access (rolling 30 minutes)
                Cache::put($cacheKey, $sessionData, self::SESSION_CACHE_TTL);

                Log::info("✅ Customer session retrieved from cache", [
                    'whatsapp_id' => $whatsappId,
                    'current_step' => $sessionData['currentStep'] ?? 'unknown',
                    'message_count' => count($sessionData['messages'] ?? [])
                ]);

                return $sessionData;
            }

            // Return default session structure if not found
            return self::createDefaultSession();

        } catch (\Exception $e) {
            Log::error('Error in getCustomerSession cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return self::createDefaultSession();
        }
    }

    /**
     * Create default session structure
     */
    private static function createDefaultSession()
    {
        return [
            'messages' => [],
            'lastUpdate' => now()->toISOString(),
            'currentStep' => 'query',
            'wantToTalkToHuman' => false,
            'customerMode' => 'normal',
            'appointment' => [
                'fullName' => '',
                'birthDate' => '',
                'idNumber' => '',
                'phoneNO' => '',
                'clinicName' => '',
                'doctorName' => '',
                'appointmentDate' => '',
                'appointmentShift' => '',
                'paymentMethod' => '',
                'appointmentConfirmed' => false
            ]
        ];
    }

    /**
     * Add new message to customer session
     * Updates session with new message and refreshes lastUpdate
     */
    public static function addMessageToSession($whatsappId, $messageData)
    {
        try {
            // Get current session
            $session = self::getCustomerSession($whatsappId);

            // Clean and sanitize message content to handle special characters and symbols
            $content = $messageData['content'] ?? '';
            $cleanContent = self::sanitizeMessageContent($content);

            // Prepare new message for session
            $newMessage = [
                'id' => $messageData['id'] ?? null,
                'content' => $cleanContent,
                'role' => $messageData['role']
            ];

            // Append new message to session messages
            $session['messages'][] = $newMessage;

            // Maintain rolling conversation limit (keep only recent messages)
            if (count($session['messages']) > self::MAX_CACHED_MESSAGES) {
                $session['messages'] = array_slice($session['messages'], -self::MAX_CACHED_MESSAGES);

                Log::info("🔄 Session messages trimmed to maintain limit", [
                    'whatsapp_id' => $whatsappId,
                    'max_messages' => self::MAX_CACHED_MESSAGES
                ]);
            }

            // Update lastUpdate timestamp
            $session['lastUpdate'] = now()->toISOString();

            // Save updated session to cache
            self::cacheSession($whatsappId, $session);

            Log::info("💬 Message added to customer session", [
                'whatsapp_id' => $whatsappId,
                'total_messages' => count($session['messages']),
                'current_step' => $session['currentStep']
            ]);

            return $session; // Return updated session

        } catch (\Exception $e) {
            Log::error('Error adding message to customer session: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'message_data' => $messageData,
                'trace' => $e->getTraceAsString()
            ]);

            return self::createDefaultSession(); // Return default session on error
        }
    }

    /**
     * Update customer session data
     * Updates specific fields in the session without affecting others
     */
    public static function updateSession($whatsappId, $updates)
    {
        try {
            // Get current session
            $session = self::getCustomerSession($whatsappId);

            // Apply updates
            foreach ($updates as $key => $value) {
                if ($key === 'appointment' && is_array($value)) {
                    // Merge appointment data
                    $session['appointment'] = array_merge($session['appointment'], $value);
                } else {
                    $session[$key] = $value;
                }
            }

            // Update lastUpdate timestamp
            $session['lastUpdate'] = now()->toISOString();

            // Save updated session to cache
            self::cacheSession($whatsappId, $session);

            Log::info("🔄 Customer session updated", [
                'whatsapp_id' => $whatsappId,
                'updated_fields' => array_keys($updates),
                'current_step' => $session['currentStep']
            ]);

            return $session;

        } catch (\Exception $e) {
            Log::error('Error updating customer session: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'updates' => $updates,
                'trace' => $e->getTraceAsString()
            ]);
            return self::getCustomerSession($whatsappId);
        }
    }

    /**
     * Get conversation formatted for AI context
     * Returns messages from session in a format suitable for AI processing
     */
    public static function getConversationForAI($whatsappId, $profileId = null)
    {
        try {
            $session = self::getCustomerSession($whatsappId);
            $messages = $session['messages'] ?? [];

            if (empty($messages)) {
                return [];
            }

            // Format messages for AI context
            $formattedMessages = array_map(function($message) {
                return [
                    'role' => $message['role'],
                    'content' => $message['content']
                ];
            }, $messages);

            return $formattedMessages;

        } catch (\Exception $e) {
            Log::error('Error formatting conversation for AI: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Cache session with TTL
     */
    private static function cacheSession($whatsappId, $session)
    {
        try {
            $cacheKey = self::SESSION_KEY_PREFIX . $whatsappId;
            Cache::put($cacheKey, $session, self::SESSION_CACHE_TTL);

            Log::info("💾 Customer session cached successfully", [
                'whatsapp_id' => $whatsappId,
                'message_count' => count($session['messages'] ?? []),
                'current_step' => $session['currentStep'] ?? 'unknown',
                'ttl_minutes' => self::SESSION_CACHE_TTL / 60,
                'cache_key' => $cacheKey
            ]);

        } catch (\Exception $e) {
            Log::error('Error caching customer session: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


    /**
     * Get session appointment data
     */
    public static function getSessionAppointment($whatsappId)
    {
        try {
            $session = self::getCustomerSession($whatsappId);
            return $session['appointment'] ?? [];

        } catch (\Exception $e) {
            Log::error('Error getting session appointment: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get session info (without messages for performance)
     */
    public static function getSessionInfo($whatsappId)
    {
        try {
            $session = self::getCustomerSession($whatsappId);

            // Return session without messages array for better performance
            return [
                'lastUpdate' => $session['lastUpdate'],
                'currentStep' => $session['currentStep'],
                'wantToTalkToHuman' => $session['wantToTalkToHuman'],
                'customerMode' => $session['customerMode'],
                'appointment' => $session['appointment'],
                'messageCount' => count($session['messages'] ?? [])
            ];

        } catch (\Exception $e) {
            Log::error('Error getting session info: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'lastUpdate' => now()->toISOString(),
                'currentStep' => 'query',
                'wantToTalkToHuman' => false,
                'customerMode' => 'normal',
                'appointment' => [],
                'messageCount' => 0
            ];
        }
    }


    /**
     * Filter problematic characters before caching
     * Removes only characters that cause issues, keeps emojis and normal symbols
     */
    private static function sanitizeMessageContent($content)
    {
        try {
            if (empty($content)) {
                return '';
            }

            // Convert to string if not already
            $content = (string) $content;

            // Remove only problematic characters that cause cache/JSON issues
            $content = str_replace([
                "\0",     // null byte
                "\x1A",   // substitute character
                "\x00",   // null
                "\x08",   // backspace
                "\x0C",   // form feed
                "�",      // replacement character (U+FFFD) - المشكلة الأساسية
                "\xEF\xBF\xBD", // UTF-8 encoding of replacement character
            ], '', $content);

            // Clean up excessive whitespace but keep normal spaces
            $content = preg_replace('/\s+/', ' ', $content);
            $content = trim($content);

            // Limit length to prevent memory issues
            if (mb_strlen($content) > 4000) {
                $content = mb_substr($content, 0, 4000) . '...';
            }

            return $content;

        } catch (\Exception $e) {
            Log::error('Error filtering message content: ' . $e->getMessage());
            return $content; // Return original if filtering fails
        }
    }
}
