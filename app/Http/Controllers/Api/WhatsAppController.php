<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WhatsApp\WhatsAppProfile;
use App\Models\WhatsApp\WhatsAppMessage;
use App\Models\WhatsApp\WhatsAppAttachment;
use App\Models\WhatsApp\WhatsAppSettings;
use App\Services\SocialMediaServices\WhatsAppCacheService;
use App\Events\WhatsAppAuthEvent;
use App\Events\WhatsAppMessaging;
use App\Jobs\WhatsAppAIResponseJob;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WhatsAppController extends Controller
{

    /**
     * Handle WhatsApp authentication status updates
     */
    public function handleAuth(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
            'status' => 'required|in:ready,authenticating,authenticated,connecting',
            'body' => 'nullable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $userId = $request->user_id;
            $status = $request->status;
            $body = $request->body;

            // Broadcast the auth event via Reverb
            broadcast(new WhatsAppAuthEvent($userId, $status, $body));
            // Handle different status types
            switch ($status) {
                case 'ready':

                    break;

                case 'authenticating':

                    break;

                case 'authenticated':
                    // Successfully connected - body contains device info
                    break;

                case 'connecting':

                    break;
            }

            return response()->json([
                'success' => true,
                'message' => 'Auth status received and broadcasted',
                'status' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing auth status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process incoming WhatsApp message - DOES EVERYTHING IN ONE CALL
     */

    public function processIncomingMessage(Request $request)
    {
        DB::transaction(function () use ($request) {
            // Log::info("Processing incoming message: ", $request->all());
            // 1. Get the WhatsApp settings for this user_id
            $waSettings = WhatsAppSettings::where('user_id', $request->user_id)->first();

            if (!$waSettings) {
                throw new \Exception('No WhatsApp settings found for user ID: ' . $request->user_id);
            }

            // 2. Get the connected user for these settings
            $connectedClient = $waSettings->clientProfile;

            if (!$connectedClient) {
                throw new \Exception('No connected WhatsApp user found for user ID: ' . $request->user_id);
            }

            // 3. Fast profile lookup/create (private chats only)
            $profileData = [
                'whatsapp_settings_id' => $waSettings->id,
                'phone_number' => $request->phone_number ?? 'unknown',
                'profile_name' => $request->profile_name ?? 'Unknown Contact',
                'is_client' => $request->is_outgoing
            ];

            // Add profile picture if provided
            if ($request->profile_picture_url) {
                $profileData['profile_picture_url'] = $request->profile_picture_url;
            }

            if ($request->platform) {
                $profileData['platform'] = $request->platform;
            }

            $profile = WhatsAppProfile::firstOrCreate(
                [
                    'whatsapp_id' => $request->whatsapp_id,
                    'whatsapp_settings_id' => $waSettings->id
                ],
                $profileData
            );

            // Update existing profile with new information if provided
            if ($profile->wasRecentlyCreated === false) {
                $updateData = [];

                // Update name if we have a better one
                if ($request->profile_name && $request->profile_name !== 'Unknown Contact' && $profile->profile_name === 'Unknown Contact') {
                    $updateData['profile_name'] = $request->profile_name;
                }

                // Update phone number if we have one and current is unknown
                if ($request->phone_number && $request->phone_number !== 'unknown' && $profile->phone_number === 'unknown') {
                    $updateData['phone_number'] = $request->phone_number;
                }

                // Update profile picture if provided
                if ($request->profile_picture_url) {
                    $updateData['profile_picture_url'] = $request->profile_picture_url;
                }

                if (!empty($updateData)) {
                    $profile->update($updateData);
                    $profile->refresh(); // Refresh to get updated data
                }
            }

            // 2. Create Message first
            $message = WhatsAppMessage::create([
                'message_id' => $request->message_id,
                'profile_id' => $profile->id,
                'type' => $this->mapMessageType($request->message_type ?? 'chat'),
                'content' => $request->message ?? '',
                'is_outgoing' => $request->is_outgoing ?? false,
                'status' =>  $request->is_outgoing ? 'sent' : 'delivered',
                'sent_at' => date('Y-m-d H:i:s', time()),
            ]);

            // 3. Handle media if present - store as attachment
            if ($request->has('media_data') && !empty($request->media_data)) {
                Log::info("Processing media data for message: " . $request->message_id);
                try {
                    // Decode base64 media data
                    $mediaData = base64_decode($request->media_data);

                    if ($mediaData) {
                        // Generate unique filename with proper extension
                        $timestamp = now()->timestamp;
                        $extension = $this->getExtensionFromMimeType($request->media_mimetype ?? 'application/octet-stream');
                        $filename = $request->media_filename ?? "media_{$timestamp}";

                        // Ensure filename has proper extension
                        if (!pathinfo($filename, PATHINFO_EXTENSION)) {
                            $filename .= ".{$extension}";
                        }

                        // Store file in public storage so it's accessible via URL
                        $filePath = "whatsapp/attachments/{$filename}";

                        Log::info("Attempting to save file: {$filePath}, size: " . strlen($mediaData) . " bytes");

                        $saved = Storage::disk('public')->put($filePath, $mediaData);

                        if ($saved) {
                            Log::info("File saved successfully to: " . Storage::disk('public')->path($filePath));

                            // Verify file exists
                            if (Storage::disk('public')->exists($filePath)) {
                                Log::info("File verified to exist at: {$filePath}");
                            } else {
                                Log::error("File does not exist after saving: {$filePath}");
                            }
                        } else {
                            Log::error("Failed to save file: {$filePath}");
                        }

                        // Create attachment record
                        $attachment = WhatsAppAttachment::create([
                            'message_id' => $message->id,
                            'filename' => $filename,
                            'mime_type' => $request->media_mimetype ?? 'application/octet-stream',
                            'file_size' => strlen($mediaData),
                        ]);

                        Log::info("Media attachment record created: {$filename}, attachment ID: " . $attachment->id);
                    } else {
                        Log::warning("Failed to decode media data for message: " . $request->message_id);
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing media for message {$request->message_id}: " . $e->getMessage());
                }
            } else {
                if ($request->has_media) {
                    Log::warning("Message has media flag but no media_data received for message: " . $request->message_id);
                }
            }

            // 4. Broadcast the event via Reverb
            broadcast(new WhatsAppMessaging(
                $request->user_id,
                $profile->id
            ));
        });

        // Process AI response asynchronously AFTER transaction is committed
        if (!$request->is_outgoing && $request->message_type == 'chat') {
            // to make sure customer want's to talk to real employee then don't auto reply
            $wantToTalkToHuman = WhatsAppCacheService::checkIfTheyWantToTalkToHuman($request->whatsapp_id);

            if ($wantToTalkToHuman) {
                return;
            }
            if ($request->phone_number == '0967714505421') {
                // Dispatch AI response job to queue OUTSIDE of transaction
                WhatsAppAIResponseJob::dispatch(
                    $request->user_id,
                    $request->message,
                    $request->whatsapp_id,
                    'text'
                );
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Message processed and broadcasted'
        ]);
    }



    private function mapMessageType($whatsappType): string
    {
        $typeMap = [
            'chat' => 'text',
            'image' => 'image',
            'video' => 'video',
            'audio' => 'audio',
            'ptt' => 'audio',
            'document' => 'document',
            'location' => 'location',
            'vcard' => 'contact',
        ];

        return $typeMap[$whatsappType] ?? 'text';
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeToExtension = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'video/mp4' => 'mp4',
            'video/avi' => 'avi',
            'video/mov' => 'mov',
            'video/quicktime' => 'mov',
            'audio/mpeg' => 'mp3',
            'audio/mp3' => 'mp3',
            'audio/wav' => 'wav',
            'audio/ogg' => 'ogg',
            'audio/aac' => 'aac',
            'application/pdf' => 'pdf',
            'application/msword' => 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'text/plain' => 'txt',
        ];

        return $mimeToExtension[$mimeType] ?? 'bin';
    }


    /**
     * Update message acknowledgment
     */
    public function updateMessageAck(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ack' => 'required|in:pending,sent,delivered,read,failed',
            'message_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $message = WhatsAppMessage::where('message_id', $request->message_id)->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => 'Message not found'
                ], 404);
            }

            $message->update(['status' => $request->ack]);

            // Broadcast the event via Reverb
            if ($message) {
                broadcast(new WhatsAppMessaging(
                    $request->user_id,
                    $message->profile_id,
                    'message_ack'
                ));
            }

            return response()->json([
                'success' => true,
                'data' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating message ACK: ' . $e->getMessage()
            ], 500);
        }
    }
}
