<?php

namespace App\Services\SocialMediaServices;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\WhatsApp\WhatsAppMessage;

class WhatsAppCacheService
{
    // Cache TTL constants
    const BUSINESS_CACHE_TTL = 72000; // 20 hours in seconds
    const MESSAGES_CACHE_TTL = 300;   // 5 minutes in seconds
    const MAX_CACHED_MESSAGES = 20;   // Maximum messages to keep in cache

    // Cache key prefixes
    const BUSINESS_KEY_PREFIX = 'whatsapp:business:';
    const MESSAGES_KEY_PREFIX = 'whatsapp:messages:';

    /**
     * Get complete business data with all relations from cache or database
     * Cache for 20 hours using user_id as key
     */
    public static function getBusinessData($userId)
    {
        try {
            $cacheKey = self::BUSINESS_KEY_PREFIX . $userId;

            // Try to get from cache first
            $cachedData = Cache::get($cacheKey);

            if ($cachedData) {
                Log::info("✅ Complete business data retrieved from cache", ['user_id' => $userId]);
                return $cachedData;
            }

            // Not in cache, get from database with all relations
            Log::info("📊 Fetching complete business data from database", ['user_id' => $userId]);

            $business = \App\Models\Business::where('user_id', $userId)
                ->with(['doctors.clinic', 'clinics.doctors', 'clinics.offers', 'clinics.services'])
                ->first();

            if (!$business) {
                Log::error('Business not found for user', ['user_id' => $userId]);
                return null;
            }

            // Build complete business data with all relations
            $businessData = [
                'name' => $business->name,
                'description' => $business->description ?? '',
                'phone' => $business->phone ?? '',
                'email' => $business->email ?? '',
                'address' => $business->address ?? '',
                'city' => $business->city ?? '',
                'website' => $business->website ?? '',
                'whatsapp_number' => $business->whatsapp_number ?? '',

                'doctors' => $business->doctors->map(function($doctor) {
                    return [
                        'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                        'specialization' => $doctor->specialization,
                        'clinic' => $doctor->clinic->name ?? 'غير محدد',
                        'available' => $doctor->is_available ? 'متاح' : 'غير متاح',
                        'experience' => $doctor->experience ?? '',
                        'education' => $doctor->education ?? '',
                    ];
                })->toArray(),

                'clinics' => $business->clinics->map(function($clinic) {
                    return [
                        'name' => $clinic->name,
                        'description' => $clinic->description ?? '',
                        'floor' => $clinic->floor_number ?? '',
                        'room' => $clinic->room_number ?? '',
                        'phone' => $clinic->phone_number ?? '',
                        'doctors_count' => $clinic->doctors->count(),
                        'doctors' => $clinic->doctors->pluck('first_name')->toArray(),
                        'services' => $clinic->services->map(function($service) {
                            return [
                                'name' => $service->name,
                                'description' => $service->description ?? '',
                                'price' => $service->price ?? 'غير محدد',
                                'is_active' => $service->is_active ?? true,
                            ];
                        })->toArray(),
                        'offers' => $clinic->offers->map(function($offer) {
                            return [
                                'title' => $offer->title,
                                'description' => $offer->description ?? '',
                                'start_date' => $offer->start_date ? $offer->start_date->format('Y-m-d') : null,
                                'end_date' => $offer->end_date ? $offer->end_date->format('Y-m-d') : null,
                                'offer_url' => $offer->offer_url ?? '',
                            ];
                        })->toArray(),
                    ];
                })->toArray(),

                'cached_at' => now()->toISOString()
            ];

            // Store in cache for 20 hours
            Cache::put($cacheKey, $businessData, self::BUSINESS_CACHE_TTL);

            Log::info("💾 Complete business data cached successfully", [
                'user_id' => $userId,
                'doctors_count' => count($businessData['doctors']),
                'clinics_count' => count($businessData['clinics'])
            ]);

            return $businessData;

        } catch (\Exception $e) {
            Log::error('Error in getBusinessData cache: ' . $e->getMessage(), [
                'user_id' => $userId,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get message history from cache or database
     * Cache for 5 minutes using whatsapp_id as key
     * Maintains rolling 20-message limit
     */
    public static function getMessageHistory($whatsappId, $profileId = null)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;

            // Try to get from cache first
            $cachedMessages = Cache::get($cacheKey);

            if ($cachedMessages) {
                // Extend cache TTL on access (rolling 5 minutes)
                Cache::put($cacheKey, $cachedMessages, self::MESSAGES_CACHE_TTL);

                // Log::info("✅ Message history retrieved from cache", [
                //     'whatsapp_id' => $whatsappId,
                //     'message_count' => count($cachedMessages)
                // ]);

                return $cachedMessages;
            }
            return [];

        } catch (\Exception $e) {
            Log::error('Error in getMessageHistory cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'profile_id' => $profileId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Add new message to cache (append to conversation)
     * Maintains rolling conversation history with message limit
     * Each WhatsApp ID has its own conversation cache
     */
    public static function addMessageToCache($whatsappId, $messageData)
    {
        try {
            // Get current conversation from cache (using whatsappId as key)
            $currentMessages = self::getMessageHistory($whatsappId);

            // Clean and sanitize message content to handle special characters and symbols
            $content = $messageData['content'] ?? '';

            // Remove or replace problematic characters that might cause issues
            $cleanContent = self::sanitizeMessageContent($content);

            // Prepare new message for cache
            $newMessage = [
                'id' => $messageData['id'] ?? null,
                'content' => $cleanContent,
                'role' => $messageData['role']
            ];

            // Append new message to conversation
            $currentMessages[] = $newMessage;

            // Maintain rolling conversation limit (keep only recent messages)
            if (count($currentMessages) > self::MAX_CACHED_MESSAGES) {
                $currentMessages = array_slice($currentMessages, -self::MAX_CACHED_MESSAGES);

                Log::info("🔄 Conversation trimmed to maintain limit", [
                    'whatsapp_id' => $whatsappId,
                    'max_messages' => self::MAX_CACHED_MESSAGES
                ]);
            }

            // Save updated conversation to cache
            self::cacheMessages($whatsappId, $currentMessages);

            // Log::info("💬 Message appended to conversation cache", [
            //     'whatsapp_id' => $whatsappId,
            //     'total_messages' => count($currentMessages),
            //     'message_content' => $newMessage['content']
            // ]);

            return $currentMessages; // Return updated conversation

        } catch (\Exception $e) {
            Log::error('Error appending message to conversation cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'message_data' => $messageData,
                'trace' => $e->getTraceAsString()
            ]);

            return []; // Return empty array on error
        }
    }

    /**
     * Get conversation formatted for AI context
     * Returns messages in a format suitable for AI processing
     */
    public static function getConversationForAI($whatsappId, $profileId = null)
    {
        try {
            $messages = self::getMessageHistory($whatsappId, $profileId);

            if (empty($messages)) {
                return [];
            }

            // Format messages for AI context
            $formattedMessages = array_map(function($message) {
                return [
                    'role' => $message['role'],
                    'content' => $message['content']
                ];
            }, $messages);

            return $formattedMessages;

        } catch (\Exception $e) {
            Log::error('Error formatting conversation for AI: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Cache messages with TTL
     */
    private static function cacheMessages($whatsappId, $messages)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;
            Cache::put($cacheKey, $messages, self::MESSAGES_CACHE_TTL);

            Log::info("💾 Conversation cached successfully", [
                'whatsapp_id' => $whatsappId,
                'message_count' => count($messages),
                'ttl_minutes' => self::MESSAGES_CACHE_TTL / 60,
                'cache_key' => $cacheKey
            ]);

        } catch (\Exception $e) {
            Log::error('Error caching conversation: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


    /**
     * Filter problematic characters before caching
     * Removes only characters that cause issues, keeps emojis and normal symbols
     */
    private static function sanitizeMessageContent($content)
    {
        try {
            if (empty($content)) {
                return '';
            }

            // Convert to string if not already
            $content = (string) $content;

            // Remove only problematic characters that cause cache/JSON issues
            $content = str_replace([
                "\0",     // null byte
                "\x1A",   // substitute character
                "\x00",   // null
                "\x08",   // backspace
                "\x0C",   // form feed
                "�",      // replacement character (U+FFFD) - المشكلة الأساسية
                "\xEF\xBF\xBD", // UTF-8 encoding of replacement character
            ], '', $content);

            // Clean up excessive whitespace but keep normal spaces
            $content = preg_replace('/\s+/', ' ', $content);
            $content = trim($content);

            // Limit length to prevent memory issues
            if (mb_strlen($content) > 4000) {
                $content = mb_substr($content, 0, 4000) . '...';
            }

            return $content;

        } catch (\Exception $e) {
            Log::error('Error filtering message content: ' . $e->getMessage());
            return $content; // Return original if filtering fails
        }
    }
}
