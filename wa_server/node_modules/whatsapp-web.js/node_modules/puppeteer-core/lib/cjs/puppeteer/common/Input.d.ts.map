{"version": 3, "file": "Input.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/Input.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EAAiC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAC/E,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,KAAK,EAAC,MAAM,eAAe,CAAC;AAMpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,qBAAa,QAAQ;;IAInB;;OAEG;IACH,UAAU,SAAK;IAEf;;OAEG;gBACS,MAAM,EAAE,UAAU;IAI9B;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACG,IAAI,CACR,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAqB,GAC3C,OAAO,CAAC,IAAI,CAAC;IA4FhB;;;;;;OAMG;IACG,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAetC;;;;;;;;;;;;;;;OAeG;IACG,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIhD,OAAO,CAAC,SAAS;IAIjB;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAgBvE;;;;;;;;;;;;;;;;;;;OAmBG;IACG,KAAK,CACT,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE;QAAC,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAM,GAC5C,OAAO,CAAC,IAAI,CAAC;CAUjB;AAED;;GAEG;AACH,oBAAY,WAAW,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC;AAE3E;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsEG;AACH,qBAAa,KAAK;;IAOhB;;OAEG;gBACS,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;IAKlD;;;;;;OAMG;IACG,IAAI,CACR,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,OAAO,GAAE;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAM,GAC7B,OAAO,CAAC,IAAI,CAAC;IAiBhB;;;;;OAKG;IACG,KAAK,CACT,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,OAAO,GAAE,YAAY,GAAG;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAM,GAC5C,OAAO,CAAC,IAAI,CAAC;IAgBhB;;;OAGG;IACG,IAAI,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAarD;;;OAGG;IACG,EAAE,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAanD;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,KAAK,CAAC,OAAO,GAAE,iBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;IAa3D;;;;OAIG;IACG,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAYzE;;;;OAIG;IACG,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAU5E;;;;OAIG;IACG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAU3E;;;;OAIG;IACG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAUvE;;;;;;;OAOG;IACG,WAAW,CACf,KAAK,EAAE,KAAK,EACZ,MAAM,EAAE,KAAK,EACb,OAAO,GAAE;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAM,GAC7B,OAAO,CAAC,IAAI,CAAC;CAajB;AAED;;;GAGG;AACH,qBAAa,WAAW;;IAItB;;OAEG;gBACS,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;IAKlD;;;;OAIG;IACG,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAa/C"}