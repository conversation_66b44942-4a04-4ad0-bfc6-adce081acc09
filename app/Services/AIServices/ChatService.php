<?php

namespace App\Services\AIServices;

use App\Services\AIServices\AIServiceFactory;
use App\Services\SocialMediaServices\WhatsAppCacheService;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Facades\Log;

class ChatService
{
    private $analyzer = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 500,
        'temperature' => 0.3,
    ];

    private $responder = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 300,
        'temperature' => 0.7,
    ];

    private $booker = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 500,
        'temperature' => 0.3,
    ];

    private $bookingService;

    public function __construct()
    {
        // $this->bookingService = new BookingService();
    }


    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {
               // 1. تحليل الطلب أولاً
            $analysis = $this->analyzeRequest($userMessage, $context);

            // تنظيف الرد من markdown formatting قبل parsing
            $result = $this->cleanJsonFromMarkdown($analysis);

            // for debugging only
            // Log::info('Parsed Result:', ['result' => $result]);
            // return $result['usefullInfo'];

            // التحقق من صحة parsing
            if (!$result || !is_array($result)) {
                Log::error('Failed to parse analysis result', ['raw' => $analysis]);
                return 'عذراً، حدث خطأ في تحليل طلبك. يرجى المحاولة مرة أخرى.';
            }

            return $result['usefullInfo'];//for testing only

            if ($result['wantToTalkToHuman']) {
                WhatsAppCacheService::updateSession($context['whatsapp_id'], ['wantToTalkToHuman' => true]);
                return 'human';
            }

            if($result['currentStep'] == 'booking')
            {
                return $this->handleBookingRequest($userMessage, $context['customer_session']['appointment'], $context);
            }

            if($result['currentStep'] == 'query')
            {
                return 'queryModel';
            }



           return $this->generateFinalResponse($userMessage, $analysis, $context);

        } catch (\Exception $e) {
            Log::error('Chat Service Error: ' . $e->getMessage(), [
                'user_message' => $userMessage,
                'context' => $context
            ]);
            return 'هذا الرد الي وسيتم الرد عليك من قبل موضف الأستقبال بأقرب وقت ممكن';
        }
    }

    /**
     * 1. تحليل الطلب
     */
    private function analyzeRequest(string $userMessage, array $context): string
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);
        $businessData = $context['business_data'] ?? [];

        $perferedResponseStructure = '
        {
        "currentStep":"هنا رد ب booking او query بحسب تحليلك لرسالته",
        "wantToTalkToHuman":true/false,
        "customerMode":"هنا من خلال تحليلك لرسالة العميل تقدر تعرف موده هل غضبان, رايق, عادي",
        "usefullInfo":"هنا ارسل اي شيء لخصته من طلب العميل مثلا العيادات او الاطباء او العروض او الخدمات او اي شيء اخر نستفيد منه عشان نعرض للعميل"
        }
        ';

        $prompt = "حلل الطلب وحدد ماذا يريد العميل:\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}\"\n\n";
        $prompt .= "اذا سئل عن خدمه معينه جيب بيانات الخدمه هذه او سئل عن طبيب او يشتي يعرف الأطباء في عيادة معينه,\n";
        $prompt .= "او استفسار عن العيادات او حجز موعد,اي شيء انت فقط افهم سؤاله وجاوبه بالبيانات حق البزنز.\n";
        $prompt .= "أرجع فقط البيانات المطلوبه وحطها في المتغير المخصص لها في نموذج الرد :\n";
        $prompt .= "بيانات البزنس:\n" . json_encode($businessData, JSON_UNESCAPED_UNICODE) . "\n\n";
        $prompt .= "ملاحظات:\n";
        $prompt .= "- اذا لم تجد شيء فقط ارجع جملة مفيده انو العميل لم يوضح طلبه\n";
        $prompt .= "- أرجع JSON فقط بدون أي تفسير أو markdown formatting\n";
        $prompt .= "- لا تستخدم ```json أو ``` في الرد\n\n";
        $prompt .= "نموذج الرد:\n{$perferedResponseStructure}\n\n";


        $response = $aiService->chat($prompt, false, $this->analyzer);
        return $response['success'] ? trim($response['content']) : 'محادثة_عامة';
    }

    /**
     * تنظيف JSON من markdown formatting
     */
    private function cleanJsonFromMarkdown(string $text): string
    {
        try {
            // إزالة markdown code blocks
            $text = preg_replace('/```json\s*/', '', $text);
            $text = preg_replace('/```\s*/', '', $text);

            // إزالة أي نص قبل أو بعد JSON
            $text = trim($text);

            // البحث عن JSON object في النص
            if (preg_match('/\{.*\}/s', $text, $matches)) {
                return trim($matches[0]);
            }

            return json_decode($text, true);

        } catch (\Exception $e) {
            Log::error('Error cleaning JSON from markdown: ' . $e->getMessage());
            return $text;
        }
    }


    /**
     * 3. إنشاء الرد النهائي
     */
    private function generateFinalResponse(string $userMessage, string $searchResult, array $context): string
    {
        $aiService = AIServiceFactory::create($this->responder['provider']);

        $businessName = $context['business_data']['name'] ?? 'العيادة';

        $systemMessage = "أنت موظف في {$businessName}.\n";
        $systemMessage .= "هناك بيانات متوفره عن العيادة والاطباء والخدمات والمواعيد والاتصالات وغيرها من البيانات المهمه.\n";
        $systemMessage .= "البيانات المتاحة:\n{$searchResult}\n\n";
        $systemMessage .= "مهمتك الرد على استفسارات العملاء بأسلوب ودود، ومفيد.\n";
        $systemMessage .= "انت سعودي الجنسيه ومرح وتستخدم اللهجه السعوديه.\n";
        $systemMessage .= "- خلك لطيف، واضح، ولا تتجاوز 200 كلمة.\n";
        $systemMessage .= "- لا تكرر المعلومات،والتحيه خاصة لو قد حييت العميل بالرسائل السابقه, وركّز على إفادة العميل بناءً على التحليل.\n";
        $systemMessage .= "- إذا ما كان فيه بيانات متاحه، اعتذر بلُطف واقترح طريقة للمساعدة. لاتجيب بيانات من راسك لأنك ممثل مجمع طبي ضروري الصدق والأمانه\n\n";

        $response = $aiService->chat($userMessage, true, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
            'system_message' => $systemMessage
        ]);

        return $response['success'] ? $response['content'] : 'مرحبا عميلنا العزيز لقد تلقينا رسالتك وسيتم الرد بأقرب وقت ممكن';
    }

    /**
     * Handle booking request
     */
    private function handleBookingRequest(string $userMessage, array $bookingIntent, array $context): string
    {
        try {
            // Process the booking
            $bookingResult = $this->bookingService->processBookingRequest($bookingIntent, $context);

            if ($bookingResult['success']) {
                $appointment = $bookingResult['booking_details'];

                $response = "تم حجز موعدك بنجاح! 🎉\n\n";
                $response .= "📅 التاريخ: " . $appointment['date'] . "\n";
                $response .= "🕐 الوقت: " . $appointment['time'] . "\n";
                $response .= "👨‍⚕️ الطبيب: د. " . $appointment['doctor_name'] . "\n";
                $response .= "🏥 العيادة: " . $appointment['clinic_name'] . "\n";
                $response .= "📝 سبب الزيارة: " . $appointment['reason'] . "\n";
                $response .= "📋 رقم الموعد: " . $appointment['appointment_id'] . "\n\n";
                $response .= "يرجى الحضور قبل الموعد بـ 15 دقيقة. شكراً لك! 🙏";

                return $response;
            } else {
                // If booking failed, try to provide helpful information
                return $this->handleBookingFailure($bookingResult['message'], $bookingIntent, $context);
            }
        } catch (\Exception $e) {
            Log::error('Error handling booking request: ' . $e->getMessage());
            return 'عذراً، حدث خطأ أثناء معالجة طلب الحجز. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.';
        }
    }

    /**
     * Handle booking failure and provide alternatives
     */
    private function handleBookingFailure(string $errorMessage, array $bookingIntent, array $context): string
    {
        $response = "عذراً، لم نتمكن من حجز الموعد: {$errorMessage}\n\n";

        // Try to get available slots if doctor was found
        if (isset($bookingIntent['clinic_name'])) {
            $availableSlots = $this->bookingService->getAvailableSlots($bookingIntent, $context);

            if (!empty($availableSlots)) {
                $response .= "المواعيد المتاحة:\n";
                $count = 0;
                foreach ($availableSlots as $slot) {
                    if ($count >= 5) break; // Show only first 5 slots
                    $response .= "⏰ " . $slot['time'] . "\n";
                    $count++;
                }
                $response .= "\nيرجى اختيار أحد هذه المواعيد.";
            } else {
                $response .= "لا توجد مواعيد متاحة حالياً. يرجى التواصل معنا مباشرة لترتيب موعد مناسب.";
            }
        }

        return $response;
    }
}
