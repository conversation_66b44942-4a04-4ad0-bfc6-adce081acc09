/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.6.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
export declare namespace Bluetooth {
    type BluetoothServiceUuid = string;
}
export declare namespace Bluetooth {
    type BluetoothManufacturerData = {
        key: number;
        data: string;
    };
}
export declare namespace Bluetooth {
    type RequestDevice = string;
}
export declare namespace Bluetooth {
    type RequestDeviceInfo = {
        id: Bluetooth.RequestDevice;
        name: string | null;
    };
}
export declare namespace Bluetooth {
    type RequestDevicePrompt = string;
}
export declare namespace Bluetooth {
    type ScanRecord = {
        name?: string;
        uuids?: [...Bluetooth.BluetoothServiceUuid[]];
        appearance?: number;
        manufacturerData?: [...Bluetooth.BluetoothManufacturerData[]];
    };
}
export type BluetoothCommand = Bluetooth.HandleRequestDevicePrompt | Bluetooth.SimulateAdapter | Bluetooth.DisableSimulation | Bluetooth.SimulatePreconnectedPeripheral | Bluetooth.SimulateAdvertisement | Bluetooth.SimulateGattConnectionResponse | Bluetooth.SimulateGattDisconnection | Record<string, never>;
export declare namespace Bluetooth {
    type HandleRequestDevicePrompt = {
        method: 'bluetooth.handleRequestDevicePrompt';
        params: Bluetooth.HandleRequestDevicePromptParameters;
    };
}
export declare namespace Bluetooth {
    type HandleRequestDevicePromptParameters = {
        context: string;
        prompt: Bluetooth.RequestDevicePrompt;
    } & (Bluetooth.HandleRequestDevicePromptAcceptParameters | Bluetooth.HandleRequestDevicePromptCancelParameters);
}
export declare namespace Bluetooth {
    type HandleRequestDevicePromptAcceptParameters = {
        accept: true;
        device: Bluetooth.RequestDevice;
    };
}
export declare namespace Bluetooth {
    type HandleRequestDevicePromptCancelParameters = {
        accept: false;
    };
}
export declare namespace Bluetooth {
    type SimulateAdapter = {
        method: 'bluetooth.simulateAdapter';
        params: Bluetooth.SimulateAdapterParameters;
    };
}
export declare namespace Bluetooth {
    type SimulateAdapterParameters = {
        context: string;
        leSupported?: boolean;
        state: 'absent' | 'powered-off' | 'powered-on';
    };
}
export declare namespace Bluetooth {
    type DisableSimulation = {
        method: 'bluetooth.disableSimulation';
        params: Bluetooth.DisableSimulationParameters;
    };
}
export declare namespace Bluetooth {
    type DisableSimulationParameters = {
        context: string;
    };
}
export declare namespace Bluetooth {
    type SimulatePreconnectedPeripheral = {
        method: 'bluetooth.simulatePreconnectedPeripheral';
        params: Bluetooth.SimulatePreconnectedPeripheralParameters;
    };
}
export declare namespace Bluetooth {
    type SimulatePreconnectedPeripheralParameters = {
        context: string;
        address: string;
        name: string;
        manufacturerData: [...Bluetooth.BluetoothManufacturerData[]];
        knownServiceUuids: [...Bluetooth.BluetoothServiceUuid[]];
    };
}
export declare namespace Bluetooth {
    type SimulateAdvertisement = {
        method: 'bluetooth.simulateAdvertisement';
        params: Bluetooth.SimulateAdvertisementParameters;
    };
}
export declare namespace Bluetooth {
    type SimulateAdvertisementParameters = {
        context: string;
        scanEntry: Bluetooth.SimulateAdvertisementScanEntryParameters;
    };
}
export declare namespace Bluetooth {
    type SimulateAdvertisementScanEntryParameters = {
        deviceAddress: string;
        rssi: number;
        scanRecord: Bluetooth.ScanRecord;
    };
}
export declare namespace Bluetooth {
    type SimulateGattConnectionResponse = {
        method: 'bluetooth.simulateGattConnectionResponse';
        params: Bluetooth.SimulateGattConnectionResponseParameters;
    };
}
export declare namespace Bluetooth {
    type SimulateGattConnectionResponseParameters = {
        context: string;
        address: string;
        code: number;
    };
}
export declare namespace Bluetooth {
    type SimulateGattDisconnection = {
        method: 'bluetooth.simulateGattDisconnection';
        params: Bluetooth.SimulateGattDisconnectionParameters;
    };
}
export declare namespace Bluetooth {
    type SimulateGattDisconnectionParameters = {
        context: string;
        address: string;
    };
}
export declare namespace Bluetooth {
    type RequestDevicePromptUpdated = {
        method: 'bluetooth.requestDevicePromptUpdated';
        params: Bluetooth.RequestDevicePromptUpdatedParameters;
    };
}
export declare namespace Bluetooth {
    type RequestDevicePromptUpdatedParameters = {
        context: string;
        prompt: Bluetooth.RequestDevicePrompt;
        devices: [...Bluetooth.RequestDeviceInfo[]];
    };
}
export declare namespace Bluetooth {
    type GattConnectionAttempted = {
        method: 'bluetooth.gattConnectionAttempted';
        params: Bluetooth.GattConnectionAttemptedParameters;
    };
}
export declare namespace Bluetooth {
    type GattConnectionAttemptedParameters = {
        context: string;
        address: string;
    };
}
