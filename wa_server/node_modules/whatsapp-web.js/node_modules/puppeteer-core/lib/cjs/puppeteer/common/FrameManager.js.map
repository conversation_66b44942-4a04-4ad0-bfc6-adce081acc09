{"version": 3, "file": "FrameManager.js", "sourceRoot": "", "sources": ["../../../../src/common/FrameManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,iDAAyC;AACzC,uDAAiD;AACjD,mDAAgE;AAChE,uDAA+C;AAC/C,+DAA8E;AAC9E,yCAAiC;AACjC,iDAAyC;AACzC,yDAA8E;AAC9E,2DAAmD;AAInD,uCAAqC;AAErC,MAAM,kBAAkB,GAAG,6BAA6B,CAAC;AAEzD;;;;;GAKG;AACU,QAAA,yBAAyB,GAAG;IACvC,aAAa,EAAE,MAAM,CAAC,4BAA4B,CAAC;IACnD,cAAc,EAAE,MAAM,CAAC,6BAA6B,CAAC;IACrD,aAAa,EAAE,MAAM,CAAC,4BAA4B,CAAC;IACnD,YAAY,EAAE,MAAM,CAAC,2BAA2B,CAAC;IACjD,cAAc,EAAE,MAAM,CAAC,6BAA6B,CAAC;IACrD,4BAA4B,EAAE,MAAM,CAClC,2CAA2C,CAC5C;IACD,uBAAuB,EAAE,MAAM,CAAC,sCAAsC,CAAC;IACvE,yBAAyB,EAAE,MAAM,CAAC,wCAAwC,CAAC;CAC5E,CAAC;AAEF;;;;GAIG;AACH,MAAa,YAAa,SAAQ,8BAAY;IAwB5C,YACE,MAAkB,EAClB,IAAU,EACV,iBAA0B,EAC1B,eAAgC;QAEhC,KAAK,EAAE,CAAC;;QA7BV,qCAAY;QACZ,+CAAgC;QAChC,gDAAkC;QAClC,2CAAsB,IAAI,GAAG,EAA4B,EAAC;QAC1D,uCAAkB,IAAI,GAAG,EAAU,EAAC;QACpC,uCAAoB;QACpB;;WAEG;QACH,eAAU,GAAG,IAAI,wBAAS,EAAE,CAAC;QAqB3B,uBAAA,IAAI,wBAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,sBAAS,IAAI,MAAA,CAAC;QAClB,uBAAA,IAAI,gCAAmB,IAAI,kCAAc,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,MAAA,CAAC;QAC3E,uBAAA,IAAI,iCAAoB,eAAe,MAAA,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,uBAAA,IAAI,4BAAQ,CAAC,CAAC;IACzC,CAAC;IAxBD,IAAI,eAAe;QACjB,OAAO,uBAAA,IAAI,qCAAiB,CAAC;IAC/B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,uBAAA,IAAI,oCAAgB,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,uBAAA,IAAI,4BAAQ,CAAC;IACtB,CAAC;IAgBO,mBAAmB,CAAC,OAAmB;QAC7C,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,CAAC,EAAE;YACvC,uBAAA,IAAI,8DAAiB,MAArB,IAAI,EAAkB,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE;YACxC,uBAAA,IAAI,+DAAkB,MAAtB,IAAI,EAAmB,KAAK,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,8BAA8B,EAAE,KAAK,CAAC,EAAE;YACjD,uBAAA,IAAI,6EAAgC,MAApC,IAAI,EAAiC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CACR,oBAAoB,EACpB,CAAC,KAAuC,EAAE,EAAE;YAC1C,uBAAA,IAAI,8DAAiB,MAArB,IAAI,EACF,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,MAAgD,CACvD,CAAC;QACJ,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE;YAC7C,uBAAA,IAAI,oEAAuB,MAA3B,IAAI,EAAwB,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE;YAC7C,uBAAA,IAAI,oEAAuB,MAA3B,IAAI,EAAwB,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE;YACpD,uBAAA,IAAI,wEAA2B,MAA/B,IAAI,EAA4B,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,mCAAmC,EAAE,KAAK,CAAC,EAAE;YACtD,uBAAA,IAAI,0EAA6B,MAAjC,IAAI,EAA8B,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAClD,uBAAA,IAAI,yEAA4B,MAAhC,IAAI,EAA6B,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE;YACxC,uBAAA,IAAI,+DAAkB,MAAtB,IAAI,EAAmB,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAqB,uBAAA,IAAI,4BAAQ;QAChD,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC;aACjC,CAAC,CAAC;YAEH,MAAM,EAAC,SAAS,EAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,uBAAA,IAAI,8DAAiB,MAArB,IAAI,EAAkB,MAAM,EAAE,SAAS,CAAC,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACtC,OAAO,uBAAA,IAAI,kEAAqB,MAAzB,IAAI,EAAsB,MAAM,EAAE,kBAAkB,CAAC,CAAC;gBAC/D,CAAC,CAAC;gBACF,yDAAyD;gBACzD,MAAM,KAAK,uBAAA,IAAI,4BAAQ;oBACrB,CAAC,CAAC,uBAAA,IAAI,oCAAgB,CAAC,UAAU,EAAE;oBACnC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;aACtB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,wEAAwE;YACxE,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,IAAI,IAAA,mCAAmB,EAAC,KAAK,CAAC,EAAE;gBACpD,OAAO;aACR;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,oBAAoB,CAClB,SAAiB,EACjB,UAAsB,uBAAA,IAAI,4BAAQ;QAElC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,SAAS,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,uBAAA,IAAI,wCAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAA,kBAAM,EAAC,OAAO,EAAE,4CAA4C,GAAG,SAAS,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAI;QACF,OAAO,uBAAA,IAAI,0BAAM,CAAC;IACpB,CAAC;IAED,SAAS;QACP,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACjD,IAAA,kBAAM,EAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QACtD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,kBAAkB,CAAC,MAAc;QAC/B,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC7C,OAAO;SACR;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;SACxC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;IAED,oBAAoB,CAAC,MAAc;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;YAC/B,kDAAkD;YAClD,mDAAmD;YACnD,uBAAA,IAAI,sEAAyB,MAA7B,IAAI,EAA0B,KAAK,CAAC,CAAC;SACtC;IACH,CAAC;CA4OF;AAtYD,oCAsYC;yXA1OmB,KAAwC;IACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACxC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,qFAEsB,OAAe;IACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;AAC5B,CAAC,qFAEsB,OAAe;IACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC1B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,yEAGC,OAAmB,EACnB,SAAkC;IAElC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE;QAC5B,uBAAA,IAAI,8DAAiB,MAArB,IAAI,EACF,OAAO,EACP,SAAS,CAAC,KAAK,CAAC,EAAE,EAClB,SAAS,CAAC,KAAK,CAAC,QAAQ,CACzB,CAAC;KACH;IACD,uBAAA,IAAI,+DAAkB,MAAtB,IAAI,EAAmB,SAAS,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;QAC1B,OAAO;KACR;IAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE;QACzC,uBAAA,IAAI,8DAAiB,MAArB,IAAI,EAAkB,OAAO,EAAE,KAAK,CAAC,CAAC;KACvC;AACH,CAAC,yEAGC,OAAmB,EACnB,OAAe,EACf,aAAqB;IAErB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChC,IAAI,KAAK,EAAE;QACT,IAAI,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;YACjC,kDAAkD;YAClD,iDAAiD;YACjD,yBAAyB;YACzB,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC7B;QACD,OAAO;KACR;IAED,KAAK,GAAG,IAAI,gBAAK,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IACzD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC5D,CAAC,mCAED,KAAK,yCAAmB,YAAiC;IACvD,MAAM,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC;IAChC,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC;IAE3C,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE7C,iCAAiC;IACjC,IAAI,KAAK,EAAE;QACT,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;YACvC,uBAAA,IAAI,sEAAyB,MAA7B,IAAI,EAA0B,KAAK,CAAC,CAAC;SACtC;KACF;IAED,+BAA+B;IAC/B,IAAI,WAAW,EAAE;QACf,IAAI,KAAK,EAAE;YACT,wEAAwE;YACxE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;SACrB;aAAM;YACL,iCAAiC;YACjC,KAAK,GAAG,IAAI,gBAAK,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,uBAAA,IAAI,4BAAQ,CAAC,CAAC;SAC3D;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KACjC;IAED,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACpD,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,sCAED,KAAK,4CAAsB,OAAmB,EAAE,IAAY;IAC1D,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;IAEtC,IAAI,uBAAA,IAAI,oCAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QACjC,OAAO;KACR;IAED,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE;QAC1D,MAAM,EAAE,iBAAiB,2CAAqB,EAAE;QAChD,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;IAEH,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE;SACV,MAAM,CAAC,KAAK,CAAC,EAAE;QACd,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC;IACrC,CAAC,CAAC;SACD,GAAG,CAAC,KAAK,CAAC,EAAE;QACX,mEAAmE;QACnE,kBAAkB;QAClB,OAAO,OAAO;aACX,IAAI,CAAC,0BAA0B,EAAE;YAChC,OAAO,EAAE,KAAK,CAAC,GAAG;YAClB,SAAS,EAAE,IAAI;YACf,mBAAmB,EAAE,IAAI;SAC1B,CAAC;aACD,KAAK,CAAC,oBAAU,CAAC,CAAC;IACvB,CAAC,CAAC,CACL,CAAC;IAEF,uBAAA,IAAI,oCAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,uGAE+B,OAAe,EAAE,GAAW;IAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACzE,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,yEAGC,OAAe,EACf,MAA8C;IAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,MAAM,KAAK,QAAQ,EAAE;QACvB,gEAAgE;QAChE,qCAAqC;QACrC,kEAAkE;QAClE,IAAI,KAAK,EAAE;YACT,uBAAA,IAAI,sEAAyB,MAA7B,IAAI,EAA0B,KAAK,CAAC,CAAC;SACtC;KACF;SAAM,IAAI,MAAM,KAAK,MAAM,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;KAC1D;AACH,CAAC,6FAGC,cAA4D,EAC5D,OAAmB;IAEnB,MAAM,OAAO,GAAG,cAAc,CAAC,OAAyC,CAAC;IACzE,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5E,IAAI,KAAgC,CAAC;IACrC,IAAI,KAAK,EAAE;QACT,sEAAsE;QACtE,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE;YAC/B,OAAO;SACR;QAED,IAAI,cAAc,CAAC,OAAO,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACnE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,6BAAU,CAAC,CAAC;SAClC;aAAM,IACL,cAAc,CAAC,IAAI,KAAK,kBAAkB;YAC1C,CAAC,KAAK,CAAC,MAAM,CAAC,kCAAe,CAAC,CAAC,UAAU,EAAE,EAC3C;YACA,0EAA0E;YAC1E,oEAAoE;YACpE,qBAAqB;YACrB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,kCAAe,CAAC,CAAC;SACvC;KACF;IACD,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAI,uBAAA,IAAI,4BAAQ,EAChC,cAAc,EACd,KAAK,CACN,CAAC;IACF,IAAI,KAAK,EAAE;QACT,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC3B;IACD,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC;IACnD,uBAAA,IAAI,wCAAoB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC7C,CAAC,iGAGC,kBAA0B,EAC1B,OAAmB;IAEnB,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,kBAAkB,EAAE,CAAC;IACpD,MAAM,OAAO,GAAG,uBAAA,IAAI,wCAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClD,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO;KACR;IACD,uBAAA,IAAI,wCAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;KAC/B;AACH,CAAC,+FAE2B,OAAmB;IAC7C,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,uBAAA,IAAI,wCAAoB,CAAC,OAAO,EAAE,EAAE;QAC/D,yDAAyD;QACzD,0BAA0B;QAC1B,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;YAC/B,SAAS;SACV;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;SAC/B;QACD,uBAAA,IAAI,wCAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACtC;AACH,CAAC,yFAEwB,KAAY;IACnC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;QACvC,uBAAA,IAAI,sEAAyB,MAA7B,IAAI,EAA0B,KAAK,CAAC,CAAC;KACtC;IACD,KAAK,CAAC,OAAO,EAAE,CAAC;IAChB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC5D,CAAC"}