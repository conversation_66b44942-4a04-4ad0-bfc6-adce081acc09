{"version": 3, "file": "QueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,+DAAkD;AAClD,yDAAiD;AACjD,yCAAiC;AACjC,yDAI4B;AAwE5B,SAAS,2BAA2B,CAClC,OAA6B;IAE7B,MAAM,eAAe,GAA0B,EAAE,CAAC;IAElD,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,cAAc,CAC3C,QAAQ,EACR,QAAQ,EACR,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC,MAAO,CAAC,aAAa,CACvD,CAAC;YACF,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC3C,IAAI,aAAa,EAAE;gBACjB,OAAO,aAAa,CAAC;aACtB;YACD,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QACF,eAAe,CAAC,OAAO,GAAG,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;YACpE,IAAI,KAAY,CAAC;YACjB,IAAI,OAAwC,CAAC;YAC7C,IAAI,cAAc,YAAY,gBAAK,EAAE;gBACnC,KAAK,GAAG,cAAc,CAAC;aACxB;iBAAM;gBACL,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;gBAC7B,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,kCAAe,CAAC,CAAC,WAAW,CACvD,cAAc,CACf,CAAC;aACH;YACD,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,kCAAe,CAAC,CAAC,sBAAsB,CACvE,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,CACR,CAAC;YACF,IAAI,OAAO,EAAE;gBACX,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;aACzB;YACD,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,IAAI,CAAC;aACb;YACD,IAAI,CAAC,CAAC,MAAM,YAAY,gCAAa,CAAC,EAAE;gBACtC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC,MAAM,CAAC,6BAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC,CAAC;KACH;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,cAAc,CAC3C,QAAQ,EACR,QAAQ,EACR,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC,MAAO,CAAC,aAAa,CACvD,CAAC;YACF,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE;gBAC1C,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC3C,IAAI,aAAa,EAAE;oBACjB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;iBAC5B;aACF;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;KACH;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,MAAM,cAAc,GAAG,2BAA2B,CAAC;IACjD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,IAAI,CAAC,CAAC,eAAe,IAAI,OAAO,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,sDAAsD,OAAO,CAAC,QAAQ,GAAG,CAC1E,CAAC;SACH;QACD,OACE,OACD,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,IAAI,CAAC,CAAC,kBAAkB,IAAI,OAAO,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CACb,yDAAyD,OAAO,CAAC,QAAQ,GAAG,CAC7E,CAAC;SACH;QACD,OAAO;YACL,GACE,OAGD,CAAC,gBAAgB,CAAC,QAAQ,CAAC;SAC7B,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,aAAa,GAAG,2BAA2B,CAAC;IAChD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAC,mBAAmB,EAAC,EAAE,EAAE;QACrD,OAAO,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAC,sBAAsB,EAAC,EAAE,EAAE;QACxD,OAAO,sBAAsB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,YAAY,GAAG,2BAA2B,CAAC;IAC/C,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAC,kBAAkB,EAAC,EAAE,EAAE;QACpD,OAAO,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAC,qBAAqB,EAAC,EAAE,EAAE;QACvD,OAAO,qBAAqB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,gBAAgB,GAAG,2BAA2B,CAAC;IACnD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAC,iBAAiB,EAAC,EAAE,EAAE;QACnD,OAAO,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAC,oBAAoB,EAAC,EAAE,EAAE;QACtD,OAAO,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC;CACF,CAAC,CAAC;AAOH,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAiC;IACtE,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,iCAAW,EAAC,CAAC;IAChC,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,aAAa,EAAC,CAAC;IACpC,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,YAAY,EAAC,CAAC;IAClC,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,gBAAgB,EAAC,CAAC;CACtC,CAAC,CAAC;AACH,MAAM,cAAc,GAAG,IAAI,GAAG,EAAkC,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,SAAgB,0BAA0B,CACxC,IAAY,EACZ,OAA2B;IAE3B,IAAI,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,kBAAkB,CAAC,CAAC;KACnE;IACD,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,kBAAkB,CAAC,CAAC;KAC1E;IAED,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACzE;IAED,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,OAAO,EAAE,2BAA2B,CAAC,OAAO,CAAC,EAAC,CAAC,CAAC;AAC5E,CAAC;AAjBD,gEAiBC;AAED;;;;GAIG;AACH,SAAgB,4BAA4B,CAAC,IAAY;IACvD,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC;AAFD,oEAEC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB;IACrC,OAAO,CAAC,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;AACpC,CAAC;AAFD,0DAEC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB;IACtC,cAAc,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAFD,4DAEC;AAED,MAAM,uBAAuB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAE3C;;GAEG;AACH,SAAgB,0BAA0B,CAAC,QAAgB;IAIzD,KAAK,MAAM,UAAU,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC,EAAE;QAClE,KAAK,MAAM,CACT,IAAI,EACJ,EAAC,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAC,EAC3C,IAAI,UAAU,EAAE;YACf,KAAK,MAAM,SAAS,IAAI,uBAAuB,EAAE;gBAC/C,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,SAAS,EAAE,CAAC;gBACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;oBAC/B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACzC,IAAI,iBAAiB,EAAE;wBACrB,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;qBACxC;oBACD,OAAO,EAAC,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAC,CAAC;iBAClD;aACF;SACF;KACF;IACD,OAAO,EAAC,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,EAAC,CAAC;AACnE,CAAC;AAtBD,gEAsBC"}