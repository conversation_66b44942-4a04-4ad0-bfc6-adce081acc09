{"version": 3, "file": "vm-interface.js", "sourceRoot": "", "sources": ["../ts/vm-interface.ts"], "names": [], "mappings": ";;;AAaA,SAAgB,SAAS,CAAO,aAAkC;IAChE,OAAO,OAAO,IAAI,aAAa,KAAK,KAAK,CAAA;AAC3C,CAAC;AAFD,8BAEC;AAED,SAAgB,MAAM,CAAO,aAAkC;IAC7D,OAAO,OAAO,IAAI,aAAa,KAAK,IAAI,CAAA;AAC1C,CAAC;AAFD,wBAEC", "sourcesContent": ["/**\n * Used as an optional.\n * `{ value: S } | { error: E }`.\n */\nexport type SuccessOrFail<S, F> =\n  | {\n      value: S\n      error?: undefined\n    }\n  | {\n      error: F\n    }\n\nexport function isSuccess<S, F>(successOrFail: SuccessOrFail<S, F>): successOrFail is { value: S } {\n  return \"error\" in successOrFail === false\n}\n\nexport function isFail<S, F>(successOrFail: SuccessOrFail<S, F>): successOrFail is { error: F } {\n  return \"error\" in successOrFail === true\n}\n\n/**\n * Used as an optional for results of a Vm call.\n * `{ value: VmHandle } | { error: VmHandle }`.\n */\nexport type VmCallResult<VmHandle> = SuccessOrFail<VmHandle, VmHandle>\n\n/**\n * A VmFunctionImplementation takes handles as arguments.\n * It should return a handle, or be void.\n *\n * To indicate an exception, a VMs can throw either a handle (transferred\n * directly) or any other Javascript value (only the poperties `name` and\n * `message` will be transferred). Or, the VmFunctionImplementation may return\n * a VmCallResult's `{ error: handle }` error variant.\n *\n * VmFunctionImplementation should not free its arguments or its return value.\n * It should not retain a reference to its return value or thrown error.\n */\nexport type VmFunctionImplementation<VmHandle> = (\n  this: VmHandle,\n  ...args: VmHandle[]\n) => VmHandle | VmCallResult<VmHandle> | void\n\n/**\n * A minimal interface to a Javascript execution environment.\n *\n * Higher-level tools should build over the LowLevelJavascriptVm interface to\n * share as much as possible between executors.\n *\n * From https://www.figma.com/blog/how-we-built-the-figma-plugin-system/\n */\nexport interface LowLevelJavascriptVm<VmHandle> {\n  global: VmHandle\n  undefined: VmHandle\n\n  typeof(handle: VmHandle): string\n\n  getNumber(handle: VmHandle): number\n  getString(handle: VmHandle): string\n\n  newNumber(value: number): VmHandle\n  newString(value: string): VmHandle\n  newObject(prototype?: VmHandle): VmHandle\n  newFunction(name: string, value: VmFunctionImplementation<VmHandle>): VmHandle\n\n  // For accessing properties of objects\n  getProp(handle: VmHandle, key: string | VmHandle): VmHandle\n  setProp(handle: VmHandle, key: string | VmHandle, value: VmHandle): void\n  defineProp(\n    handle: VmHandle,\n    key: string | VmHandle,\n    descriptor: VmPropertyDescriptor<VmHandle>\n  ): void\n\n  callFunction(func: VmHandle, thisVal: VmHandle, ...args: VmHandle[]): VmCallResult<VmHandle>\n  evalCode(code: string, filename?: string): VmCallResult<VmHandle>\n}\n\n/**\n * From https://www.figma.com/blog/how-we-built-the-figma-plugin-system/\n */\nexport interface VmPropertyDescriptor<VmHandle> {\n  value?: VmHandle\n  configurable?: boolean\n  enumerable?: boolean\n  get?: (this: VmHandle) => VmHandle\n  set?: (this: VmHandle, value: VmHandle) => void\n}\n"]}